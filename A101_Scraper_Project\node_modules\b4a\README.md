# Buffer for <PERSON><PERSON><PERSON>

Buffer for Array (B4A) provides a set of functions for bridging the gap between the Node.js `<PERSON>uffer` class and the `Uint8Array` class. A browser compatibility layer is also included, making it possible to use B4A in both Node.js and browsers without having to worry about whether you're dealing with buffers or typed arrays.

## Installation

```sh
npm install b4a
```

## API

#### `b4a.isBuffer(value)`

See https://nodejs.org/api/buffer.html#static-method-bufferisbufferobj

This will also return `true` when passed a `Uint8Array`.

#### `b4a.isEncoding(encoding)`

See https://nodejs.org/api/buffer.html#static-method-bufferisencodingencoding

#### `b4a.alloc(size[, fill[, encoding]])`

See https://nodejs.org/api/buffer.html#static-method-bufferallocsize-fill-encoding

#### `b4a.allocUnsafe(size)`

See https://nodejs.org/api/buffer.html#static-method-bufferallocunsafesize

#### `b4a.allocUnsafeSlow(size)`

See https://nodejs.org/api/buffer.html#static-method-bufferallocunsafeslowsize

#### `b4a.byteLength(string)`

See https://nodejs.org/api/buffer.html#static-method-bufferbytelengthstring-encoding

#### `b4a.compare(buf1, buf2)`

See https://nodejs.org/api/buffer.html#static-method-buffercomparebuf1-buf2

#### `b4a.concat(buffers[, totalLength])`

See https://nodejs.org/api/buffer.html#static-method-bufferconcatlist-totallength

#### `b4a.copy(source, target[, targetStart[, sourceStart[, sourceEnd]]])`

See https://nodejs.org/api/buffer.html#bufcopytarget-targetstart-sourcestart-sourceend

#### `b4a.equals(buf1, buf2)`

See https://nodejs.org/api/buffer.html#bufequalsotherbuffer

#### `b4a.fill(buffer, value[, offset[, end]][, encoding])`

See https://nodejs.org/api/buffer.html#buffillvalue-offset-end-encoding

#### `b4a.from(array)`

See https://nodejs.org/api/buffer.html#static-method-bufferfromarray

#### `b4a.from(arrayBuffer[, byteOffset[, length]])`

See https://nodejs.org/api/buffer.html#static-method-bufferfromarraybuffer-byteoffset-length

#### `b4a.from(buffer)`

See https://nodejs.org/api/buffer.html#static-method-bufferfrombuffer

#### `b4a.from(string[, encoding])`

See https://nodejs.org/api/buffer.html#static-method-bufferfromstring-encoding

#### `b4a.includes(buffer, value[, byteOffset][, encoding])`

See https://nodejs.org/api/buffer.html#bufincludesvalue-byteoffset-encoding

#### `b4a.indexOf(buffer, value[, byteOffset][, encoding])`

See https://nodejs.org/api/buffer.html#bufindexofvalue-byteoffset-encoding

#### `b4a.lastIndexOf(buffer, value[, byteOffset][, encoding])`

See https://nodejs.org/api/buffer.html#buflastindexofvalue-byteoffset-encoding

#### `b4a.swap16(buffer)`

See https://nodejs.org/api/buffer.html#bufswap16

#### `b4a.swap32(buffer)`

See https://nodejs.org/api/buffer.html#bufswap32

#### `b4a.swap64(buffer)`

See https://nodejs.org/api/buffer.html#bufswap64

#### `b4a.toBuffer(buffer)`

Convert a buffer to its canonical representation. In Node.js, the canonical representation is a `Buffer`. In the browser, the canonical representation is a `Uint8Array`.

#### `b4a.toString(buffer, [encoding[, start[, end]]])`

See https://nodejs.org/api/buffer.html#buftostringencoding-start-end

#### `b4a.write(buffer, string[, offset[, length]][, encoding])`

See https://nodejs.org/api/buffer.html#bufwritestring-offset-length-encoding

#### `b4a.writeDoubleLE(buffer, value[, offset])`

See https://nodejs.org/api/buffer.html#bufwritedoublelevalue-offset

#### `b4a.writeFloatLE(buffer, value[, offset])`

See https://nodejs.org/api/buffer.html#bufwritefloatlevalue-offset

#### `b4a.writeUInt32LE(buffer, value[, offset])`

https://nodejs.org/api/buffer.html#bufwriteuint32levalue-offset

#### `b4a.writeInt32LE(buffer, value[, offset])`

See https://nodejs.org/api/buffer.html#bufwriteint32levalue-offset

#### `b4a.readDoubleLE(buffer[, offset])`

See https://nodejs.org/api/buffer.html#bufreaddoubleleoffset

#### `b4a.readFloatLE(buffer[, offset])`

See https://nodejs.org/api/buffer.html#bufreadfloatleoffset

#### `b4a.readUInt32LE(buffer[, offset])`

See https://nodejs.org/api/buffer.html#bufreaduint32leoffset

#### `b4a.readInt32LE(buffer[, offset])`

See https://nodejs.org/api/buffer.html#bufreadint32leoffset

## License

Apache 2.0
