import sys
import time
import os
import json
import logging
from datetime import datetime
from typing import List, Dict, Optional
import requests
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from PyQt5.QtWidgets import (QApplication, QMainWindow, QPushButton, QVBoxLayout, 
                            QWidget, QListWidget, QLabel, QProgressBar, QHBoxLayout,
                            QListWidgetItem, QGridLayout, QComboBox, QSpinBox, 
                            QCheckBox, QFileDialog, QMessageBox, QTextEdit, QTabWidget,
                            QGroupBox, QFormLayout, QLineEdit)
from PyQt5.QtCore import Qt, QThr<PERSON>, pyqtSignal, QSize, QTimer
from PyQt5.QtGui import QPixmap, QImage, QFont

# Logging konfigürasyonu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('a101_scraper.log'),
        logging.StreamHandler()
    ]
)

class Config:
    """Konfigürasyon sınıfı"""
    def __init__(self):
        self.categories = {
            "10 TL Ürünleri": "https://www.a101.com.tr/10-tl-urunleri",
            "15 TL Ürünleri": "https://www.a101.com.tr/15-tl-urunleri", 
            "20 TL Ürünleri": "https://www.a101.com.tr/20-tl-urunleri",
            "Aktüel Ürünler": "https://www.a101.com.tr/aktuel-urunler",
            "Gıda": "https://www.a101.com.tr/gida",
            "Temizlik": "https://www.a101.com.tr/temizlik"
        }
        self.selectors = {
            "product_container": "ul.grid > div",
            "product_name": "div > a > div > header > hgroup > h3",
            "current_price": "div > a > div > div > div > div > div > div > div > span",
            "final_price": "div > a > section > span:nth-child(2)",
            "product_image": "div > a > div > div > div > div > div > img",
            "product_link": "div > a"
        }
        self.retry_count = 3
        self.wait_timeout = 15
        self.page_load_delay = 5
        self.headless = True
        
    def load_from_file(self, filename: str = "config.json"):
        """Konfigürasyonu dosyadan yükle"""
        try:
            if os.path.exists(filename):
                with open(filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.__dict__.update(data)
                logging.info(f"Konfigürasyon {filename} dosyasından yüklendi")
        except Exception as e:
            logging.error(f"Konfigürasyon yükleme hatası: {e}")
    
    def save_to_file(self, filename: str = "config.json"):
        """Konfigürasyonu dosyaya kaydet"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.__dict__, f, indent=4, ensure_ascii=False)
            logging.info(f"Konfigürasyon {filename} dosyasına kaydedildi")
        except Exception as e:
            logging.error(f"Konfigürasyon kaydetme hatası: {e}")

class Product:
    def __init__(self, name: str, price: str, image_url: str, product_url: str = ""):
        self.name = name
        self.price = price
        self.image_url = image_url
        self.product_url = product_url
        self.image_data = None
        self.scraped_at = datetime.now().isoformat()
        
    def to_dict(self) -> Dict:
        """Ürünü dictionary'ye çevir"""
        return {
            "name": self.name,
            "price": self.price,
            "image_url": self.image_url,
            "product_url": self.product_url,
            "scraped_at": self.scraped_at
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'Product':
        """Dictionary'den ürün oluştur"""
        product = cls(data["name"], data["price"], data["image_url"], data.get("product_url", ""))
        product.scraped_at = data.get("scraped_at", datetime.now().isoformat())
        return product

class ScraperThread(QThread):
    update_signal = pyqtSignal(str)
    product_signal = pyqtSignal(Product)
    finished_signal = pyqtSignal(list)
    error_signal = pyqtSignal(str)
    
    def __init__(self, config: Config, category_url: str):
        super().__init__()
        self.config = config
        self.category_url = category_url
        self.should_stop = False
        
    def stop(self):
        """Thread'i durdur"""
        self.should_stop = True
        
    def setup_driver(self) -> webdriver.Chrome:
        """WebDriver'ı konfigüre et ve başlat"""
        chrome_options = Options()
        
        if self.config.headless:
            chrome_options.add_argument("--headless")
        
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        try:
            from webdriver_manager.chrome import ChromeDriverManager
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            return driver
        except Exception as e:
            raise WebDriverException(f"WebDriver başlatma hatası: {e}")

    def run(self):
        """Ana scraping işlemi"""
        driver = None
        products = []
        
        for attempt in range(self.config.retry_count):
            if self.should_stop:
                break
                
            try:
                self.update_signal.emit(f"Deneme {attempt + 1}/{self.config.retry_count}")
                
                # WebDriver'ı başlat
                driver = self.setup_driver()
                self.update_signal.emit("WebDriver başlatıldı")
                
                # Sayfaya git
                self.update_signal.emit(f"Sayfa yükleniyor: {self.category_url}")
                driver.get(self.category_url)
                
                # Sayfanın yüklenmesini bekle
                self.update_signal.emit("Sayfa yükleniyor...")
                time.sleep(self.config.page_load_delay)
                
                # Ürün elementlerinin yüklenmesini bekle
                try:
                    WebDriverWait(driver, self.config.wait_timeout).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, self.config.selectors["product_container"]))
                    )
                    self.update_signal.emit("Ürün elementleri bulundu")
                except TimeoutException:
                    self.update_signal.emit("Ürün elementleri bulunamadı, alternatif seçiciler deneniyor...")
                    # Alternatif seçiciler dene
                    alternative_selectors = [
                        "div.product-item",
                        ".product-card",
                        "[data-product]",
                        ".grid-item"
                    ]
                    
                    for selector in alternative_selectors:
                        try:
                            WebDriverWait(driver, 5).until(
                                EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                            )
                            self.config.selectors["product_container"] = selector
                            self.update_signal.emit(f"Alternatif seçici bulundu: {selector}")
                            break
                        except TimeoutException:
                            continue
                    else:
                        raise TimeoutException("Hiçbir ürün seçici bulunamadı")
                
                # Tüm ürünleri yüklemek için sayfayı kaydır
                self.update_signal.emit("Tüm ürünler yükleniyor...")
                self.scroll_to_load_all_products(driver)

                # Ürünleri çek
                products = self.scrape_products(driver)
                
                if products:
                    self.update_signal.emit(f"Başarılı! {len(products)} ürün bulundu")
                    break
                else:
                    self.update_signal.emit("Ürün bulunamadı, tekrar deneniyor...")
                    
            except Exception as e:
                error_msg = f"Hata (Deneme {attempt + 1}): {str(e)}"
                self.update_signal.emit(error_msg)
                logging.error(error_msg)
                
                if attempt == self.config.retry_count - 1:
                    self.error_signal.emit(f"Tüm denemeler başarısız: {str(e)}")
                    
            finally:
                if driver:
                    try:
                        driver.quit()
                    except:
                        pass
                    driver = None
                    
                if attempt < self.config.retry_count - 1:
                    self.update_signal.emit("5 saniye bekleniyor...")
                    time.sleep(5)
        
        self.finished_signal.emit(products)
    
    def scrape_products(self, driver) -> List[Product]:
        """Ürünleri çek"""
        products = []
        self.update_signal.emit("Ürünler çekiliyor...")

        # Ürün konteynerlerini bul
        try:
            product_containers = driver.find_elements(By.CSS_SELECTOR, self.config.selectors["product_container"])
            self.update_signal.emit(f"{len(product_containers)} ürün konteyneri bulundu")
        except Exception as e:
            self.update_signal.emit(f"Ürün konteynerleri bulunamadı: {e}")
            return products

        for i, container in enumerate(product_containers, 1):
            if self.should_stop:
                break

            try:
                # Ürün adını çek
                name = self.extract_text(container, self.config.selectors["product_name"])
                if not name:
                    # Alternatif seçiciler dene
                    alternative_name_selectors = ["h3", ".product-name", "[data-name]", "a[title]"]
                    for selector in alternative_name_selectors:
                        name = self.extract_text(container, selector)
                        if name:
                            break

                # Fiyatı çek
                price = self.extract_price(container)

                # Resim URL'sini çek
                image_url = self.extract_image_url(container)

                # Ürün linkini çek
                product_url = self.extract_product_url(container)

                if name and price:
                    product = Product(name, price, image_url, product_url)

                    # Resmi indir (opsiyonel)
                    if image_url:
                        try:
                            response = requests.get(image_url, timeout=10)
                            if response.status_code == 200:
                                product.image_data = response.content
                        except Exception as img_error:
                            self.update_signal.emit(f"Resim indirme hatası: {img_error}")

                    products.append(product)
                    self.product_signal.emit(product)
                    self.update_signal.emit(f"Bulundu: {name} - {price}")

            except Exception as container_error:
                self.update_signal.emit(f"Konteyner {i} işleme hatası: {container_error}")
                logging.error(f"Konteyner {i} hatası: {container_error}")

        return products

    def extract_text(self, container, selector: str) -> str:
        """Element'ten metin çek"""
        try:
            element = container.find_element(By.CSS_SELECTOR, selector)
            return element.text.strip()
        except:
            return ""

    def extract_price(self, container) -> str:
        """Fiyat bilgisini çek"""
        # Önce final price'ı dene
        price = self.extract_text(container, self.config.selectors["final_price"])

        if not price or price == "N/A":
            # Current price'ı dene
            price = self.extract_text(container, self.config.selectors["current_price"])

        if not price or price == "N/A":
            # Alternatif fiyat seçicileri
            alternative_price_selectors = [
                ".price", ".product-price", "[data-price]",
                "span[class*='price']", "div[class*='price']"
            ]

            for selector in alternative_price_selectors:
                price = self.extract_text(container, selector)
                if price and price != "N/A":
                    break

        return price if price and price != "N/A" else "Fiyat Bulunamadı"

    def extract_image_url(self, container) -> str:
        """Resim URL'sini çek"""
        try:
            img_element = container.find_element(By.CSS_SELECTOR, self.config.selectors["product_image"])
            return img_element.get_attribute("data-src") or img_element.get_attribute("src") or ""
        except:
            # Alternatif resim seçicileri
            alternative_img_selectors = ["img", ".product-image img", "[data-src]"]

            for selector in alternative_img_selectors:
                try:
                    img_element = container.find_element(By.CSS_SELECTOR, selector)
                    url = img_element.get_attribute("data-src") or img_element.get_attribute("src")
                    if url:
                        return url
                except:
                    continue

            return ""

    def extract_product_url(self, container) -> str:
        """Ürün linkini çek"""
        try:
            link_element = container.find_element(By.CSS_SELECTOR, self.config.selectors["product_link"])
            href = link_element.get_attribute("href")
            return href if href else ""
        except:
            # Alternatif link seçicileri
            alternative_link_selectors = ["a", "[href]"]

            for selector in alternative_link_selectors:
                try:
                    link_element = container.find_element(By.CSS_SELECTOR, selector)
                    href = link_element.get_attribute("href")
                    if href:
                        return href
                except:
                    continue

            return ""

    def scroll_to_load_all_products(self, driver):
        """Sayfayı kaydırarak tüm ürünleri yükle"""
        last_height = driver.execute_script("return document.body.scrollHeight")
        scroll_attempts = 0
        max_scroll_attempts = 25  # Maksimum kaydırma sayısı

        self.update_signal.emit("Dinamik yükleme için sayfa kaydırılıyor...")

        while scroll_attempts < max_scroll_attempts and not self.should_stop:
            # Sayfanın sonuna kaydır
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")

            # Yeni içeriğin yüklenmesi için bekle
            time.sleep(2)

            # Yeni yüksekliği kontrol et
            new_height = driver.execute_script("return document.body.scrollHeight")

            # Ürün sayısını kontrol et
            current_products = len(driver.find_elements(By.CSS_SELECTOR, self.config.selectors["product_container"]))
            self.update_signal.emit(f"Kaydırma {scroll_attempts + 1}: {current_products} ürün yüklendi")

            # Eğer sayfa yüksekliği değişmediyse, daha fazla içerik yok demektir
            if new_height == last_height:
                # Son bir kez daha dene
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)
                final_height = driver.execute_script("return document.body.scrollHeight")

                if final_height == new_height:
                    self.update_signal.emit("Tüm ürünler yüklendi (sayfa sonu)")
                    break
                else:
                    new_height = final_height

            last_height = new_height
            scroll_attempts += 1

            # "Daha fazla yükle" veya "Load More" butonu varsa tıkla
            try:
                load_more_selectors = [
                    "[class*='load-more']",
                    "[class*='show-more']",
                    "button[onclick*='load']",
                    ".load-more-btn",
                    "#load-more",
                    "button:contains('Daha Fazla')",
                    "button:contains('Load More')"
                ]

                for selector in load_more_selectors:
                    try:
                        load_more_button = driver.find_element(By.CSS_SELECTOR, selector)
                        if load_more_button.is_displayed() and load_more_button.is_enabled():
                            driver.execute_script("arguments[0].click();", load_more_button)
                            self.update_signal.emit(f"'{selector}' butonuna tıklandı")
                            time.sleep(3)
                            break
                    except:
                        continue
            except:
                pass  # Buton yoksa devam et

            # Sayfanın ortasına da kaydır (lazy loading için)
            if scroll_attempts % 3 == 0:
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight / 2);")
                time.sleep(1)

        if scroll_attempts >= max_scroll_attempts:
            self.update_signal.emit(f"Maksimum kaydırma sayısına ulaşıldı ({max_scroll_attempts})")

        # Son ürün sayısını bildir
        final_products = len(driver.find_elements(By.CSS_SELECTOR, self.config.selectors["product_container"]))
        self.update_signal.emit(f"Toplam {final_products} ürün konteyneri yüklendi")

        # Sayfanın başına dön
        driver.execute_script("window.scrollTo(0, 0);")
        time.sleep(1)

class ProductListItem(QWidget):
    def __init__(self, product: Product):
        super().__init__()
        self.product = product
        self.initUI()

    def initUI(self):
        layout = QHBoxLayout()

        # Ürün resmi
        image_label = QLabel()
        if self.product.image_data:
            pixmap = QPixmap()
            pixmap.loadFromData(self.product.image_data)
            pixmap = pixmap.scaled(80, 80, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            image_label.setPixmap(pixmap)
        else:
            image_label.setText("Resim Yok")
            image_label.setStyleSheet("border: 1px solid gray; background-color: #f0f0f0;")
        image_label.setFixedSize(80, 80)
        image_label.setAlignment(Qt.AlignCenter)

        # Ürün bilgileri
        info_layout = QVBoxLayout()

        name_label = QLabel(self.product.name)
        name_label.setWordWrap(True)
        name_label.setFont(QFont("Arial", 10, QFont.Bold))

        price_label = QLabel(self.product.price)
        price_label.setStyleSheet("font-weight: bold; color: #e74c3c; font-size: 12pt;")

        # Ürün URL'si varsa göster
        if self.product.product_url:
            url_label = QLabel(f"URL: {self.product.product_url[:50]}...")
            url_label.setStyleSheet("color: #3498db; font-size: 8pt;")
            url_label.setWordWrap(True)
            info_layout.addWidget(url_label)

        # Çekilme tarihi
        scraped_label = QLabel(f"Çekilme: {self.product.scraped_at[:19]}")
        scraped_label.setStyleSheet("color: #7f8c8d; font-size: 8pt;")

        info_layout.addWidget(name_label)
        info_layout.addWidget(price_label)
        info_layout.addWidget(scraped_label)
        info_layout.addStretch()

        layout.addWidget(image_label)
        layout.addLayout(info_layout, 1)

        self.setLayout(layout)

class ConfigDialog(QWidget):
    def __init__(self, config: Config, parent=None):
        super().__init__(parent)
        self.config = config
        self.setWindowTitle("Konfigürasyon")
        self.setGeometry(200, 200, 500, 400)
        self.initUI()

    def initUI(self):
        layout = QVBoxLayout()

        # Genel ayarlar
        general_group = QGroupBox("Genel Ayarlar")
        general_layout = QFormLayout()

        self.headless_checkbox = QCheckBox("Headless Mode")
        self.headless_checkbox.setChecked(self.config.headless)

        self.retry_spinbox = QSpinBox()
        self.retry_spinbox.setRange(1, 10)
        self.retry_spinbox.setValue(self.config.retry_count)

        self.timeout_spinbox = QSpinBox()
        self.timeout_spinbox.setRange(5, 60)
        self.timeout_spinbox.setValue(self.config.wait_timeout)

        self.delay_spinbox = QSpinBox()
        self.delay_spinbox.setRange(1, 30)
        self.delay_spinbox.setValue(self.config.page_load_delay)

        general_layout.addRow("Headless Mode:", self.headless_checkbox)
        general_layout.addRow("Tekrar Sayısı:", self.retry_spinbox)
        general_layout.addRow("Bekleme Süresi (sn):", self.timeout_spinbox)
        general_layout.addRow("Sayfa Yükleme Gecikmesi (sn):", self.delay_spinbox)

        general_group.setLayout(general_layout)

        # Butonlar
        button_layout = QHBoxLayout()

        save_btn = QPushButton("Kaydet")
        save_btn.clicked.connect(self.save_config)

        cancel_btn = QPushButton("İptal")
        cancel_btn.clicked.connect(self.close)

        button_layout.addWidget(save_btn)
        button_layout.addWidget(cancel_btn)

        layout.addWidget(general_group)
        layout.addLayout(button_layout)

        self.setLayout(layout)

    def save_config(self):
        self.config.headless = self.headless_checkbox.isChecked()
        self.config.retry_count = self.retry_spinbox.value()
        self.config.wait_timeout = self.timeout_spinbox.value()
        self.config.page_load_delay = self.delay_spinbox.value()
        self.config.save_to_file()

        QMessageBox.information(self, "Başarılı", "Konfigürasyon kaydedildi!")
        self.close()

class A101ScraperImproved(QMainWindow):
    def __init__(self):
        super().__init__()
        self.config = Config()
        self.config.load_from_file()
        self.products = []
        self.scraper_thread = None
        self.config_dialog = None

        self.setWindowTitle("A101 Scraper - Geliştirilmiş Versiyon")
        self.setGeometry(100, 100, 1000, 700)
        self.initUI()

    def initUI(self):
        main_widget = QWidget()
        layout = QVBoxLayout()

        # Başlık
        title_label = QLabel("A101 Ürün Çekici - Geliştirilmiş Versiyon")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18pt; font-weight: bold; color: #2c3e50; margin: 10px;")

        # Kontrol paneli
        control_panel = self.create_control_panel()

        # Tab widget
        self.tab_widget = QTabWidget()

        # Ürünler sekmesi
        products_tab = QWidget()
        products_layout = QVBoxLayout()

        # Ürün listesi
        self.results_list = QListWidget()
        self.results_list.setSpacing(5)

        # Durum etiketi
        self.status_label = QLabel("Kategori seçin ve 'Başlat' butonuna tıklayın")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: #7f8c8d; font-size: 12pt; margin: 10px;")

        products_layout.addWidget(self.results_list)
        products_layout.addWidget(self.status_label)
        products_tab.setLayout(products_layout)

        # Log sekmesi
        log_tab = QWidget()
        log_layout = QVBoxLayout()

        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setReadOnly(True)

        clear_log_btn = QPushButton("Logları Temizle")
        clear_log_btn.clicked.connect(self.clear_logs)

        log_layout.addWidget(QLabel("İşlem Logları:"))
        log_layout.addWidget(self.log_text)
        log_layout.addWidget(clear_log_btn)
        log_tab.setLayout(log_layout)

        # Sekmeleri ekle
        self.tab_widget.addTab(products_tab, "Ürünler")
        self.tab_widget.addTab(log_tab, "Loglar")

        # Layout'a ekle
        layout.addWidget(title_label)
        layout.addWidget(control_panel)
        layout.addWidget(self.tab_widget, 1)

        main_widget.setLayout(layout)
        self.setCentralWidget(main_widget)

    def create_control_panel(self) -> QWidget:
        """Kontrol panelini oluştur"""
        panel = QGroupBox("Kontrol Paneli")
        layout = QGridLayout()

        # Kategori seçimi
        layout.addWidget(QLabel("Kategori:"), 0, 0)
        self.category_combo = QComboBox()
        self.category_combo.addItems(list(self.config.categories.keys()))
        layout.addWidget(self.category_combo, 0, 1)

        # Butonlar
        self.start_btn = QPushButton("Başlat")
        self.start_btn.clicked.connect(self.start_scraping)
        self.start_btn.setStyleSheet("QPushButton { background-color: #27ae60; color: white; font-weight: bold; padding: 8px; }")

        self.stop_btn = QPushButton("Durdur")
        self.stop_btn.clicked.connect(self.stop_scraping)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("QPushButton { background-color: #e74c3c; color: white; font-weight: bold; padding: 8px; }")

        config_btn = QPushButton("Ayarlar")
        config_btn.clicked.connect(self.show_config)
        config_btn.setStyleSheet("QPushButton { background-color: #3498db; color: white; font-weight: bold; padding: 8px; }")

        save_btn = QPushButton("Kaydet")
        save_btn.clicked.connect(self.save_products)
        save_btn.setStyleSheet("QPushButton { background-color: #f39c12; color: white; font-weight: bold; padding: 8px; }")

        layout.addWidget(self.start_btn, 0, 2)
        layout.addWidget(self.stop_btn, 0, 3)
        layout.addWidget(config_btn, 1, 0)
        layout.addWidget(save_btn, 1, 1)

        # Progress bar
        self.progress = QProgressBar()
        self.progress.setVisible(False)
        layout.addWidget(self.progress, 1, 2, 1, 2)

        panel.setLayout(layout)
        return panel

    def start_scraping(self):
        """Scraping işlemini başlat"""
        if self.scraper_thread and self.scraper_thread.isRunning():
            return

        # UI'yi güncelle
        self.results_list.clear()
        self.products = []
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress.setVisible(True)
        self.progress.setRange(0, 0)  # Belirsiz progress
        self.status_label.setText("Ürünler çekiliyor...")

        # Seçilen kategoriyi al
        selected_category = self.category_combo.currentText()
        category_url = self.config.categories[selected_category]

        # Thread'i başlat
        self.scraper_thread = ScraperThread(self.config, category_url)
        self.scraper_thread.update_signal.connect(self.update_log)
        self.scraper_thread.product_signal.connect(self.add_product)
        self.scraper_thread.finished_signal.connect(self.scraping_finished)
        self.scraper_thread.error_signal.connect(self.handle_error)
        self.scraper_thread.start()

        self.update_log(f"Scraping başlatıldı: {selected_category}")

    def stop_scraping(self):
        """Scraping işlemini durdur"""
        if self.scraper_thread and self.scraper_thread.isRunning():
            self.scraper_thread.stop()
            self.update_log("Durdurma komutu gönderildi...")

            # Thread'in durmasını bekle
            QTimer.singleShot(3000, self.force_stop_thread)

    def force_stop_thread(self):
        """Thread'i zorla durdur"""
        if self.scraper_thread and self.scraper_thread.isRunning():
            self.scraper_thread.terminate()
            self.scraper_thread.wait()
            self.update_log("Thread zorla durduruldu")

        self.scraping_finished([])

    def update_log(self, message: str):
        """Log mesajı ekle"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.log_text.append(formatted_message)
        logging.info(message)

    def add_product(self, product: Product):
        """Ürün listesine ürün ekle"""
        self.products.append(product)

        # Liste widget'ına ekle
        item = QListWidgetItem()
        product_widget = ProductListItem(product)
        item.setSizeHint(product_widget.sizeHint())

        self.results_list.addItem(item)
        self.results_list.setItemWidget(item, product_widget)

        # Durum etiketini güncelle
        self.status_label.setText(f"Toplam {len(self.products)} ürün bulundu")

    def scraping_finished(self, products: List[Product]):
        """Scraping işlemi tamamlandı"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress.setVisible(False)

        if products:
            self.status_label.setText(f"Tamamlandı! Toplam {len(products)} ürün bulundu")
            self.update_log(f"Scraping tamamlandı: {len(products)} ürün")
        else:
            self.status_label.setText("Hiçbir ürün bulunamadı")
            self.update_log("Scraping tamamlandı: Ürün bulunamadı")

    def handle_error(self, error_message: str):
        """Hata durumunu işle"""
        self.update_log(f"HATA: {error_message}")
        QMessageBox.critical(self, "Hata", f"Bir hata oluştu:\n{error_message}")

    def show_config(self):
        """Konfigürasyon penceresini göster"""
        if not self.config_dialog:
            self.config_dialog = ConfigDialog(self.config, self)

        self.config_dialog.show()
        self.config_dialog.raise_()
        self.config_dialog.activateWindow()

    def save_products(self):
        """Ürünleri dosyaya kaydet"""
        if not self.products:
            QMessageBox.warning(self, "Uyarı", "Kaydedilecek ürün bulunamadı!")
            return

        filename, _ = QFileDialog.getSaveFileName(
            self, "Ürünleri Kaydet",
            f"a101_products_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "JSON Files (*.json);;All Files (*)"
        )

        if filename:
            try:
                products_data = [product.to_dict() for product in self.products]
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(products_data, f, indent=4, ensure_ascii=False)

                QMessageBox.information(self, "Başarılı", f"Ürünler kaydedildi:\n{filename}")
                self.update_log(f"Ürünler kaydedildi: {filename}")

            except Exception as e:
                QMessageBox.critical(self, "Hata", f"Kaydetme hatası:\n{str(e)}")
                self.update_log(f"Kaydetme hatası: {e}")

    def clear_logs(self):
        """Logları temizle"""
        self.log_text.clear()
        self.update_log("Loglar temizlendi")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = A101ScraperImproved()
    window.show()
    sys.exit(app.exec_())
