/**
 * Copyright 2023 Google LLC.
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * Returns the normalized key value for a given key according to the table:
 * https://w3c.github.io/webdriver/#dfn-normalized-key-value
 */
export declare function getNormalizedKey(value: string): string;
/**
 * Returns the key code for a given key according to the table:
 * https://w3c.github.io/webdriver/#dfn-shifted-character
 */
export declare function getKeyCode(key: string): string | undefined;
/**
 * Returns the location of the key according to the table:
 * https://w3c.github.io/webdriver/#dfn-key-location
 */
export declare function getKeyLocation(key: string): 0 | 1 | 2 | 3;
