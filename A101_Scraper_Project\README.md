# A101 Scraper Project

Bu proje A101 mağazasından ürün bilgilerini çekmek ve fiyat güncellemeleri yapmak için geliştirilmiştir.

## Dosyalar

### Python Dosyaları
- **a101_scraper.py**: A101'den 10 TL ürünlerini çeken PyQt5 tabanlı GUI uygulaması
- **web_scraper.py**: Genel web scraping aracı (div.text-2xl elementlerini çeker)

### JavaScript Dosyaları
- **a101_price_updater.js**: Puppeteer kullanarak A101 ürün fiyatlarını güncelleyen script

### Veri Dosyaları
- **saved_qr_codes.json**: QR kod verilerinin saklandığı JSON dosyası
- **package.json**: Node.js bağımlılıkları
- **package-lock.json**: Node.js bağımlı<PERSON><PERSON>k kilidi

## Kurulum

### Python Bağımlılıkları
```bash
pip install selenium webdriver-manager requests beautifulsoup4 PyQt5
```

### Node.js Bağımlılıkları
```bash
npm install
```

## Kullanım

### A101 Scraper (Python)
```bash
python a101_scraper.py
```
- GUI açılır
- "Ürünleri Getir" butonuna tıklayın
- A101'den 10 TL ürünleri çekilir ve görüntülenir

### Fiyat Güncelleyici (JavaScript)
```bash
node a101_price_updater.js
```
- saved_qr_codes.json dosyasındaki ürünlerin fiyatlarını günceller
- Puppeteer ile A101 sitesinden güncel fiyatları çeker

### Web Scraper (Python)
```bash
python web_scraper.py
```
- Genel web scraping aracı
- Herhangi bir URL'den div.text-2xl elementlerini çeker

## Özellikler

### A101 Scraper
- PyQt5 GUI arayüzü
- Selenium WebDriver ile dinamik içerik çekme
- Ürün resimlerini indirme
- Real-time log görüntüleme
- Çoklu thread desteği

### Fiyat Güncelleyici
- Puppeteer ile headless browser
- Çerez yönetimi
- Otomatik fiyat güncelleme
- JSON veri saklama
- Hata yönetimi

### Web Scraper
- Selenium + BeautifulSoup kombinasyonu
- CSS seçici desteği
- JSON export
- GUI arayüzü

## Gereksinimler

### Python
- Python 3.7+
- selenium
- webdriver-manager
- requests
- beautifulsoup4
- PyQt5

### Node.js
- Node.js 14+
- puppeteer
- fs (built-in)
- path (built-in)

## Yeni Özellikler (Geliştirilmiş Versiyon)

### a101_scraper_improved.py
- **Gelişmiş GUI**: Sekmeli arayüz, konfigürasyon paneli
- **Çoklu Kategori Desteği**: 10 TL, 15 TL, 20 TL, Aktüel, Gıda, Temizlik vb.
- **Konfigürasyon Yönetimi**: JSON tabanlı ayar sistemi
- **Retry Mekanizması**: Başarısız istekleri otomatik tekrar deneme
- **Alternatif Seçiciler**: Site yapısı değiştiğinde otomatik adaptasyon
- **Gelişmiş Logging**: Dosya ve konsol logları
- **Veri Kaydetme**: JSON formatında ürün verilerini kaydetme
- **Hata Yönetimi**: Detaylı hata raporlama ve kullanıcı bildirimleri

### Kullanım Kılavuzu

#### Geliştirilmiş Scraper'ı Çalıştırma
```bash
cd A101_Scraper_Project
python a101_scraper_improved.py
```

#### Konfigürasyon Ayarlama
1. Uygulamayı açın
2. "Ayarlar" butonuna tıklayın
3. İstediğiniz ayarları yapın:
   - Headless Mode: Tarayıcıyı görünmez modda çalıştırır
   - Tekrar Sayısı: Başarısız istekleri kaç kez tekrar deneyeceği
   - Bekleme Süresi: Elementlerin yüklenmesi için maksimum bekleme süresi
   - Sayfa Yükleme Gecikmesi: Sayfa yüklendikten sonra bekleme süresi

#### Ürün Çekme
1. Kategori seçin (10 TL Ürünleri, Aktüel Ürünler vb.)
2. "Başlat" butonuna tıklayın
3. İşlem sırasında "Loglar" sekmesinden detayları takip edin
4. İşlem tamamlandığında ürünler "Ürünler" sekmesinde görünür

#### Veri Kaydetme
1. Ürünler çekildikten sonra "Kaydet" butonuna tıklayın
2. Dosya adı ve konumu seçin
3. Veriler JSON formatında kaydedilir

### Dosya Yapısı
```
A101_Scraper_Project/
├── a101_scraper.py              # Orijinal scraper
├── a101_scraper_improved.py     # Geliştirilmiş scraper
├── a101_price_updater.js        # Fiyat güncelleyici
├── web_scraper.py               # Genel web scraper
├── test_scraper.py              # Test scripti
├── config.json                  # Konfigürasyon dosyası
├── requirements.txt             # Python bağımlılıkları
├── package.json                 # Node.js bağımlılıkları
├── saved_qr_codes.json          # QR kod verileri
└── README.md                    # Bu dosya
```

## Notlar

- Chrome WebDriver otomatik olarak indirilir
- A101 sitesinin yapısı değişirse CSS seçicilerin güncellenmesi gerekebilir
- Fiyat güncelleyici çalışırken internet bağlantısı gereklidir
- Geliştirilmiş versiyon daha stabil ve kullanıcı dostudur
- Konfigürasyon dosyası ile ayarlar kalıcı olarak saklanır
