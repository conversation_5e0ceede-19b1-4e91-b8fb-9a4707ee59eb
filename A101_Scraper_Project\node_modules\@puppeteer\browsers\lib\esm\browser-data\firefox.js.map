{"version": 3, "file": "firefox.js", "sourceRoot": "", "sources": ["../../../src/browser-data/firefox.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,MAAM,SAAS,CAAC;AACzB,OAAO,IAAI,MAAM,WAAW,CAAC;AAE7B,OAAO,EAAC,OAAO,EAAC,MAAM,gBAAgB,CAAC;AAEvC,OAAO,EAAC,eAAe,EAAsB,MAAM,YAAY,CAAC;AAEhE,SAAS,SAAS,CAAC,OAAe;IAChC,MAAM,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,EAAG,CAAC,CAAC;IACzD,OAAO,YAAY,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;AAC5C,CAAC;AAED,SAAS,cAAc,CAAC,QAAyB,EAAE,OAAe;IAChE,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,eAAe,CAAC,KAAK;YACxB,OAAO,WAAW,OAAO,2BAA2B,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC;QAC3E,KAAK,eAAe,CAAC,SAAS;YAC5B,OAAO,WAAW,OAAO,4BAA4B,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC;QAC5E,KAAK,eAAe,CAAC,OAAO,CAAC;QAC7B,KAAK,eAAe,CAAC,GAAG;YACtB,OAAO,WAAW,OAAO,gBAAgB,CAAC;QAC5C,KAAK,eAAe,CAAC,KAAK,CAAC;QAC3B,KAAK,eAAe,CAAC,KAAK;YACxB,OAAO,WAAW,OAAO,UAAU,QAAQ,MAAM,CAAC;IACtD,CAAC;AACH,CAAC;AAED,SAAS,OAAO,CAAC,QAAyB,EAAE,OAAe;IACzD,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,eAAe,CAAC,SAAS,CAAC;QAC/B,KAAK,eAAe,CAAC,KAAK;YACxB,OAAO,WAAW,OAAO,QAAQ,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC;QACxD,KAAK,eAAe,CAAC,OAAO,CAAC;QAC7B,KAAK,eAAe,CAAC,GAAG;YACtB,OAAO,WAAW,OAAO,MAAM,CAAC;QAClC,KAAK,eAAe,CAAC,KAAK,CAAC;QAC3B,KAAK,eAAe,CAAC,KAAK;YACxB,OAAO,iBAAiB,OAAO,MAAM,CAAC;IAC1C,CAAC;AACH,CAAC;AAED,SAAS,YAAY,CAAC,QAAyB;IAC7C,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,eAAe,CAAC,KAAK;YACxB,OAAO,cAAc,CAAC;QACxB,KAAK,eAAe,CAAC,SAAS;YAC5B,OAAO,eAAe,CAAC;QACzB,KAAK,eAAe,CAAC,OAAO,CAAC;QAC7B,KAAK,eAAe,CAAC,GAAG;YACtB,OAAO,KAAK,CAAC;QACf,KAAK,eAAe,CAAC,KAAK,CAAC;QAC3B,KAAK,eAAe,CAAC,KAAK;YACxB,OAAO,QAAQ,CAAC;IACpB,CAAC;AACH,CAAC;AAED,SAAS,YAAY,CAAC,OAAe;IACnC,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC;QAClD,IAAI,OAAO,CAAC,UAAU,CAAC,KAAK,GAAG,GAAG,CAAC,EAAE,CAAC;YACpC,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC9C,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;IACD,qDAAqD;IACrD,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAC3C,CAAC;AAED,MAAM,UAAU,kBAAkB,CAChC,QAAyB,EACzB,OAAe,EACf,OAAgB;IAEhB,MAAM,CAAC,OAAO,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;IACxC,QAAQ,OAAO,EAAE,CAAC;QAChB,KAAK,cAAc,CAAC,OAAO;YACzB,OAAO;gBACL,wEAAwE,CAAC;YAC3E,MAAM;QACR,KAAK,cAAc,CAAC,UAAU;YAC5B,OAAO,KAAK,qDAAqD,CAAC;YAClE,MAAM;QACR,KAAK,cAAc,CAAC,IAAI,CAAC;QACzB,KAAK,cAAc,CAAC,MAAM,CAAC;QAC3B,KAAK,cAAc,CAAC,GAAG;YACrB,OAAO,KAAK,kDAAkD,CAAC;YAC/D,MAAM;IACV,CAAC;IACD,OAAO,GAAG,OAAO,IAAI,mBAAmB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;AAC1E,CAAC;AAED,MAAM,UAAU,mBAAmB,CACjC,QAAyB,EACzB,OAAe;IAEf,MAAM,CAAC,OAAO,EAAE,eAAe,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;IACzD,QAAQ,OAAO,EAAE,CAAC;QAChB,KAAK,cAAc,CAAC,OAAO;YACzB,OAAO,CAAC,cAAc,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAC;QACrD,KAAK,cAAc,CAAC,UAAU,CAAC;QAC/B,KAAK,cAAc,CAAC,IAAI,CAAC;QACzB,KAAK,cAAc,CAAC,MAAM,CAAC;QAC3B,KAAK,cAAc,CAAC,GAAG;YACrB,OAAO;gBACL,eAAe;gBACf,YAAY,CAAC,QAAQ,CAAC;gBACtB,OAAO;gBACP,OAAO,CAAC,QAAQ,EAAE,eAAe,CAAC;aACnC,CAAC;IACN,CAAC;AACH,CAAC;AAED,MAAM,UAAU,sBAAsB,CACpC,QAAyB,EACzB,OAAe;IAEf,MAAM,CAAC,OAAO,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;IACxC,QAAQ,OAAO,EAAE,CAAC;QAChB,KAAK,cAAc,CAAC,OAAO;YACzB,QAAQ,QAAQ,EAAE,CAAC;gBACjB,KAAK,eAAe,CAAC,OAAO,CAAC;gBAC7B,KAAK,eAAe,CAAC,GAAG;oBACtB,OAAO,IAAI,CAAC,IAAI,CACd,qBAAqB,EACrB,UAAU,EACV,OAAO,EACP,SAAS,CACV,CAAC;gBACJ,KAAK,eAAe,CAAC,SAAS,CAAC;gBAC/B,KAAK,eAAe,CAAC,KAAK;oBACxB,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;gBACzC,KAAK,eAAe,CAAC,KAAK,CAAC;gBAC3B,KAAK,eAAe,CAAC,KAAK;oBACxB,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;YAC/C,CAAC;QACH,KAAK,cAAc,CAAC,IAAI,CAAC;QACzB,KAAK,cAAc,CAAC,UAAU,CAAC;QAC/B,KAAK,cAAc,CAAC,GAAG,CAAC;QACxB,KAAK,cAAc,CAAC,MAAM;YACxB,QAAQ,QAAQ,EAAE,CAAC;gBACjB,KAAK,eAAe,CAAC,OAAO,CAAC;gBAC7B,KAAK,eAAe,CAAC,GAAG;oBACtB,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;gBAClE,KAAK,eAAe,CAAC,SAAS,CAAC;gBAC/B,KAAK,eAAe,CAAC,KAAK;oBACxB,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;gBACzC,KAAK,eAAe,CAAC,KAAK,CAAC;gBAC3B,KAAK,eAAe,CAAC,KAAK;oBACxB,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;YAC5C,CAAC;IACL,CAAC;AACH,CAAC;AAED,MAAM,CAAN,IAAY,cAMX;AAND,WAAY,cAAc;IACxB,mCAAiB,CAAA;IACjB,6BAAW,CAAA;IACX,2CAAyB,CAAA;IACzB,+BAAa,CAAA;IACb,qCAAmB,CAAA;AACrB,CAAC,EANW,cAAc,KAAd,cAAc,QAMzB;AAED,MAAM,CAAC,KAAK,UAAU,cAAc,CAClC,UAA0B,cAAc,CAAC,OAAO;IAEhD,MAAM,mBAAmB,GAAG;QAC1B,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,aAAa;QACnC,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,wBAAwB;QACjD,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,oBAAoB;QACjD,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,oBAAoB;QAC3C,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,iBAAiB;KAC5C,CAAC;IACF,MAAM,QAAQ,GAAG,CAAC,MAAM,OAAO,CAC7B,IAAI,GAAG,CAAC,+DAA+D,CAAC,CACzE,CAA2B,CAAC;IAC7B,MAAM,OAAO,GAAG,QAAQ,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC;IACvD,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,MAAM,IAAI,KAAK,CAAC,WAAW,OAAO,gBAAgB,CAAC,CAAC;IACtD,CAAC;IACD,OAAO,OAAO,GAAG,GAAG,GAAG,OAAO,CAAC;AACjC,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,aAAa,CAAC,OAAuB;IACzD,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;QACjC,MAAM,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE;YACpC,SAAS,EAAE,IAAI;SAChB,CAAC,CAAC;IACL,CAAC;IACD,MAAM,eAAe,CAAC;QACpB,WAAW,EAAE;YACX,GAAG,yBAAyB,CAAC,OAAO,CAAC,WAAW,CAAC;YACjD,GAAG,OAAO,CAAC,WAAW;SACvB;QACD,IAAI,EAAE,OAAO,CAAC,IAAI;KACnB,CAAC,CAAC;AACL,CAAC;AAED,SAAS,yBAAyB,CAChC,UAAmC;IAEnC,MAAM,MAAM,GAAG,YAAY,CAAC;IAE5B,MAAM,YAAY,GAAG;QACnB,4CAA4C;QAC5C,sBAAsB,EAAE,EAAE;QAC1B,6CAA6C;QAC7C,6BAA6B,EAAE,KAAK;QACpC,0CAA0C;QAC1C,+BAA+B,EAAE,IAAI;QAErC,wDAAwD;QACxD,8BAA8B,EAAE,KAAK;QAErC,+CAA+C;QAC/C,yEAAyE;QACzE,2CAA2C,EACzC,uCAAuC;QAEzC,+DAA+D;QAC/D,UAAU;QACV,uDAAuD;QACvD,iCAAiC,EAAE,IAAI;QACvC,qBAAqB;QACrB,4DAA4D,EAAE,KAAK;QACnE,8BAA8B;QAC9B,4BAA4B,EAAE,KAAK;QACnC,iEAAiE;QACjE,oCAAoC;QACpC,2CAA2C,EAAE,IAAI;QAEjD,mCAAmC;QACnC,0CAA0C,EAAE,KAAK;QACjD,wCAAwC,EAAE,KAAK;QAC/C,sCAAsC,EAAE,KAAK;QAC7C,uCAAuC,EAAE,KAAK;QAE9C,qCAAqC;QACrC,uBAAuB,EAAE,KAAK;QAC9B,sEAAsE;QACtE,wCAAwC,EAAE,KAAK;QAC/C,4CAA4C;QAC5C,mCAAmC,EAAE,KAAK;QAE1C,qBAAqB;QACrB,0BAA0B,EAAE,aAAa;QACzC,sEAAsE;QACtE,0CAA0C,EAAE,QAAQ;QACpD,sCAAsC;QACtC,sBAAsB,EAAE,CAAC;QAEzB,yEAAyE;QACzE,yEAAyE;QACzE,WAAW;QACX,6CAA6C,EAAE,KAAK;QACpD,+CAA+C;QAC/C,mCAAmC,EAAE,KAAK;QAC1C,gDAAgD;QAChD,yBAAyB,EAAE,KAAK;QAEhC,wEAAwE;QACxE,yCAAyC,EAAE,KAAK;QAEhD,uBAAuB;QACvB,wBAAwB,EAAE,KAAK;QAC/B,uEAAuE;QACvE,uBAAuB;QACvB,iCAAiC,EAAE,KAAK;QACxC,8CAA8C;QAC9C,kCAAkC,EAAE,EAAE;QACtC,kCAAkC;QAClC,oBAAoB,EAAE,KAAK;QAE3B,6CAA6C;QAC7C,8CAA8C,EAAE,UAAU,MAAM,sBAAsB;QACtF,mDAAmD,EAAE,KAAK;QAC1D,4CAA4C,EAAE,KAAK;QACnD,6CAA6C,EAAE,KAAK;QACpD,0CAA0C,EAAE,KAAK;QAEjD,gFAAgF;QAChF,4CAA4C,EAAE,KAAK;QACnD,6DAA6D,EAAE,IAAI;QAEnE,gFAAgF;QAChF,gEAAgE;QAChE,2BAA2B,EAAE,KAAK;QAElC,wBAAwB;QACxB,8BAA8B,EAAE,KAAK;QAErC,qEAAqE;QACrE,yDAAyD;QACzD,wBAAwB,EAAE,IAAI;QAE9B,iCAAiC;QACjC,4BAA4B,EAAE,KAAK;QAEnC,gCAAgC;QAChC,gCAAgC,EAAE,CAAC;QACnC,yBAAyB,EAAE,CAAC;QAE5B,6DAA6D;QAC7D,8DAA8D;QAC9D,8BAA8B,EAAE,CAAC;QACjC,0BAA0B,EAAE,CAAC;QAE7B,4DAA4D;QAC5D,oCAAoC,EAAE,KAAK;QAE3C,6DAA6D;QAC7D,gCAAgC,EAAE,KAAK;QAEvC,yDAAyD;QACzD,2BAA2B,EAAE,KAAK;QAElC,yDAAyD;QACzD,8BAA8B,EAAE,KAAK;QAErC,0DAA0D;QAC1D,mCAAmC,EAAE,UAAU,MAAM,qBAAqB;QAE1E,qEAAqE;QACrE,uBAAuB,EAAE,IAAI;QAE7B,4BAA4B;QAC5B,mCAAmC,EAAE,KAAK;QAE1C,qEAAqE;QACrE,mDAAmD;QACnD,sBAAsB,EAAE,IAAI;QAE5B,mBAAmB;QACnB,eAAe,EAAE,KAAK;QAEtB,kBAAkB;QAClB,qBAAqB,EAAE,CAAC;QAExB,uDAAuD;QACvD,kCAAkC,EAAE,IAAI;QAExC,+DAA+D;QAC/D,iCAAiC,EAAE,KAAK;QAExC,gCAAgC;QAChC,4BAA4B,EAAE,IAAI;QAElC,iEAAiE;QACjE,sCAAsC,EAAE,KAAK;QAE7C,wCAAwC;QACxC,mCAAmC,EAAE,KAAK;QAE1C,sEAAsE;QACtE,+BAA+B;QAC/B,yCAAyC,EAAE,CAAC;QAE5C,yDAAyD;QACzD,+BAA+B,EAAE,KAAK;QAEtC,iDAAiD;QACjD,oBAAoB,EAAE,MAAM;QAE5B,iBAAiB;QACjB,oBAAoB,EAAE,CAAC;QAEvB,oCAAoC,EAAE,KAAK;QAE3C,wDAAwD;QACxD,uDAAuD;QACvD,gBAAgB,EAAE,IAAI;QAEtB,iCAAiC;QACjC,uCAAuC,EAAE,KAAK;QAE9C,gDAAgD;QAChD,0CAA0C,EAAE,KAAK;QAEjD,4DAA4D;QAC5D,+BAA+B;QAC/B,uCAAuC,EAAE,KAAK;QAE9C,yDAAyD;QACzD,oCAAoC,EAAE,CAAC;QAEvC,kDAAkD;QAClD,0BAA0B,EAAE,UAAU,MAAM,mBAAmB;QAE/D,mEAAmE;QACnE,YAAY;QACZ,sBAAsB,EAAE,KAAK;QAE7B,qEAAqE;QACrE,uEAAuE;QACvE,wBAAwB,EAAE,KAAK;QAE/B,iCAAiC;QACjC,8BAA8B,EAAE,aAAa;QAE7C,iCAAiC;QACjC,yCAAyC,EAAE,EAAE;QAE7C,gEAAgE;QAChE,oCAAoC,EAAE,KAAK;QAE3C,4DAA4D;QAC5D,qCAAqC,EAAE,CAAC,CAAC;KAC1C,CAAC;IAEF,OAAO,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;AACjD,CAAC;AAED,KAAK,UAAU,UAAU,CAAC,KAAa;IACrC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;QAC1B,OAAO;IACT,CAAC;IACD,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,GAAG,YAAY,CAAC,CAAC;AAC1D,CAAC;AAED;;;;;;GAMG;AACH,KAAK,UAAU,eAAe,CAAC,OAAuB;IACpD,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;IACtD,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAEpD,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;QACrE,OAAO,aAAa,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;IACxE,CAAC,CAAC,CAAC;IAEH,wCAAwC;IACxC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC;QACtC,UAAU,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;YACnC,MAAM,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1D,CAAC,CAAC;QACF,UAAU,CAAC,SAAS,CAAC;KACtB,CAAC,CAAC;IACH,KAAK,MAAM,OAAO,IAAI,MAAM,EAAE,CAAC;QAC7B,IAAI,OAAO,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;YAClC,MAAM,OAAO,CAAC,MAAM,CAAC;QACvB,CAAC;IACH,CAAC;AACH,CAAC;AAED,MAAM,UAAU,eAAe,CAAC,CAAS,EAAE,CAAS;IAClD,2CAA2C;IAC3C,OAAO,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;AAC7E,CAAC"}