{"version": 3, "file": "BrowserProcessor.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/modules/browser/BrowserProcessor.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAIH,+DAKuC;AAMvC,MAAa,gBAAgB;IAClB,iBAAiB,CAAY;IAC7B,uBAAuB,CAAyB;IAChD,mBAAmB,CAAqB;IAEjD,YACE,gBAA2B,EAC3B,sBAA8C,EAC9C,kBAAsC;QAEtC,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;QACtD,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;IAChD,CAAC;IAED,KAAK;QACH,sDAAsD;QACtD,6DAA6D;QAC7D,UAAU,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC;QAEzE,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,iBAAiB,CACrB,MAA2B;QAE3B,MAAM,OAAO,GAAgD;YAC3D,WAAW,EAAE,MAAM,CAAC,kBAAkB,CAAC,IAAI,SAAS;SACrD,CAAC;QACF,MAAM,eAAe,GACnB,MAAM,CAAC,sBAAsB,CAAC,IAAI,SAAS,CAAC;QAC9C,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtD,CAAC;QACD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CACtD,6BAA6B,EAC7B,OAAO,CACR,CAAC;QACF,OAAO;YACL,WAAW,EAAE,OAAO,CAAC,gBAAgB;SACtC,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,iBAAiB,CACrB,MAA2C;QAE3C,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;QACvC,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;YAC9B,MAAM,IAAI,sCAAwB,CAChC,0CAA0C,CAC3C,CAAC;QACJ,CAAC;QACD,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,8BAA8B,EAAE;gBACvE,gBAAgB,EAAE,WAAW;aAC9B,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,mKAAmK;YACnK,IAAK,GAAa,CAAC,OAAO,CAAC,UAAU,CAAC,gCAAgC,CAAC,EAAE,CAAC;gBACxE,MAAM,IAAI,wCAA0B,CAAE,GAAa,CAAC,OAAO,CAAC,CAAC;YAC/D,CAAC;YACD,MAAM,GAAG,CAAC;QACZ,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,eAAe;QACnB,OAAO;YACL,YAAY,EAAE,MAAM,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE;SAC/D,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,QAAgB;QACnC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CACzD,4BAA4B,EAC5B,EAAC,QAAQ,EAAC,CACX,CAAC;QACF,OAAO;YACL,wCAAwC;YACxC,MAAM,EAAE,KAAK;YACb,YAAY,EAAE,GAAG,UAAU,CAAC,QAAQ,EAAE;YACtC,KAAK,EAAE,UAAU,CAAC,MAAM,CAAC,WAAW,IAAI,QAAQ;YAChD,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC;YACrC,KAAK,EAAE,UAAU,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC;YACnC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;YAC9B,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;SAC9B,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,MAAM,iBAAiB,GAAG,IAAI,CAAC,uBAAuB;aACnD,mBAAmB,EAAE;aACrB,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QAE9B,MAAM,aAAa,GAAG,MAAM,OAAO,CAAC,GAAG,CACrC,iBAAiB,CAAC,GAAG,CACnB,KAAK,EAAE,QAAQ,EAAE,EAAE,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CACxD,CACF,CAAC;QAEF,MAAM,qBAAqB,GAAG,IAAI,GAAG,EAAU,CAAC;QAChD,MAAM,mBAAmB,GAAG,IAAI,KAAK,EAA4B,CAAC;QAElE,wCAAwC;QACxC,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE,CAAC;YACnC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC;gBACpD,qBAAqB,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;gBAC/C,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACnC,CAAC;QACH,CAAC;QACD,OAAO,EAAC,aAAa,EAAE,mBAAmB,EAAC,CAAC;IAC9C,CAAC;CACF;AAhHD,4CAgHC"}