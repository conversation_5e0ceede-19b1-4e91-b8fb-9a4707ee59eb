import sys
import time
import os
import json
import logging
from datetime import datetime
from typing import List, Dict, Optional
import requests
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutEx<PERSON>, NoSuchElementException, WebDriverException
from PyQt5.QtWidgets import (QApplication, QMainWindow, QPushButton, QVBoxLayout,
                            QWidget, QListWidget, QLabel, QProgressBar, QHBoxLayout,
                            QListWidgetItem, QGridLayout, QComboBox, QSpinBox,
                            QCheckBox, QFileDialog, QMessageBox, QTextEdit, QTabWidget,
                            QGroupBox, QFormLayout, QLineEdit)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QSize, QTimer
from PyQt5.QtGui import QPixmap, QImage, QFont

class Product:
    def __init__(self, name, price, image_url):
        self.name = name
        self.price = price
        self.image_url = image_url
        self.image_data = None

class ScraperThread(QThread):
    update_signal = pyqtSignal(str)
    product_signal = pyqtSignal(Product)
    finished_signal = pyqtSignal(list)
    
    def run(self):
        driver = None
        try:
            # Configure Chrome options
            chrome_options = Options()
            chrome_options.add_argument("--headless")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
            
            # Initialize the driver with service
            from webdriver_manager.chrome import ChromeDriverManager
            from selenium.webdriver.chrome.service import Service
            
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # Navigate to the URL
            url = "https://www.a101.com.tr/10-tl-urunleri"
            self.update_signal.emit(f"Navigating to {url}...")
            driver.get(url)
            
            # Wait for the page to load
            self.update_signal.emit("Waiting for page to load...")
            time.sleep(5)  # Give time for JavaScript to execute

            # Wait for product elements to be present
            try:
                WebDriverWait(driver, 15).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "ul.grid > div"))
                )
            except Exception as wait_error:
                self.update_signal.emit(f"Wait error: {str(wait_error)}")

            # Scroll down to load all products
            self.update_signal.emit("Loading all products by scrolling...")
            self.scroll_to_load_all_products(driver)

            # Get products
            products = []
            self.update_signal.emit("Searching for products...")

            # Find all product containers
            product_containers = driver.find_elements(By.CSS_SELECTOR, "ul.grid > div")
            self.update_signal.emit(f"Found {len(product_containers)} product containers")
            
            for i, container in enumerate(product_containers, 1):
                try:
                    # Using the exact selectors provided
                    # Extract product name
                    name_selector = "div > a > div > header > hgroup > h3"
                    name_element = container.find_element(By.CSS_SELECTOR, name_selector)
                    name = name_element.text.strip()
                    
                    # Extract current price (discounted price)
                    current_price_selector = "div > a > div > div > div > div > div > div > div > span"
                    try:
                        current_price_element = container.find_element(By.CSS_SELECTOR, current_price_selector)
                        current_price = current_price_element.text.strip()
                    except:
                        current_price = "N/A"
                    
                    # Extract original price (crossed out)
                    original_price_selector = "div > a > div > div > div > div > div > div > div > span"
                    try:
                        original_price_element = container.find_element(By.CSS_SELECTOR, original_price_selector)
                        original_price = original_price_element.text.strip()
                    except:
                        original_price = ""
                    
                    # Extract final price (bottom price)
                    final_price_selector = "div > a > section > span:nth-child(2)"
                    try:
                        final_price_element = container.find_element(By.CSS_SELECTOR, final_price_selector)
                        final_price = final_price_element.text.strip()
                    except:
                        final_price = current_price if current_price != "N/A" else "N/A"
                    
                    # Determine which price to use
                    price = final_price if final_price != "N/A" else current_price
                    
                    # Extract image URL
                    image_selector = "div > a > div > div > div > div > div > img"
                    try:
                        image_element = container.find_element(By.CSS_SELECTOR, image_selector)
                        image_url = image_element.get_attribute("data-src") or image_element.get_attribute("src")
                    except:
                        # Try alternative image selector
                        try:
                            image_element = container.find_element(By.CSS_SELECTOR, "div img")
                            image_url = image_element.get_attribute("data-src") or image_element.get_attribute("src")
                        except:
                            image_url = ""
                    
                    if name and price and image_url:
                        product = Product(name, price, image_url)
                        
                        # Download image
                        try:
                            if image_url:
                                response = requests.get(image_url)
                                if response.status_code == 200:
                                    product.image_data = response.content
                        except Exception as img_error:
                            self.update_signal.emit(f"Image download error: {str(img_error)}")
                        
                        products.append(product)
                        self.product_signal.emit(product)
                        self.update_signal.emit(f"Found: {name} - {price}")
                except Exception as container_error:
                    self.update_signal.emit(f"Error processing container {i}: {str(container_error)}")
            
            self.finished_signal.emit(products)
            
            # Debug info if no products found
            if not products:
                self.update_signal.emit(f"No products found. Page title: {driver.title}")
                self.update_signal.emit(f"Page source length: {len(driver.page_source)} characters")
                
        except Exception as e:
            self.update_signal.emit(f"Error: {str(e)}")
        finally:
            if driver:
                driver.quit()

    def scroll_to_load_all_products(self, driver):
        """Sayfayı kaydırarak tüm ürünleri yükle"""
        last_height = driver.execute_script("return document.body.scrollHeight")
        scroll_attempts = 0
        max_scroll_attempts = 20  # Maksimum kaydırma sayısı

        while scroll_attempts < max_scroll_attempts:
            # Sayfanın sonuna kaydır
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")

            # Yeni içeriğin yüklenmesi için bekle
            time.sleep(3)

            # Yeni yüksekliği kontrol et
            new_height = driver.execute_script("return document.body.scrollHeight")

            # Ürün sayısını kontrol et
            current_products = len(driver.find_elements(By.CSS_SELECTOR, "ul.grid > div"))
            self.update_signal.emit(f"Scroll {scroll_attempts + 1}: {current_products} ürün yüklendi")

            # Eğer sayfa yüksekliği değişmediyse, daha fazla içerik yok demektir
            if new_height == last_height:
                # Son bir kez daha dene
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)
                final_height = driver.execute_script("return document.body.scrollHeight")

                if final_height == new_height:
                    self.update_signal.emit("Tüm ürünler yüklendi (sayfa sonu)")
                    break
                else:
                    new_height = final_height

            last_height = new_height
            scroll_attempts += 1

            # "Daha fazla yükle" butonu varsa tıkla
            try:
                load_more_button = driver.find_element(By.CSS_SELECTOR, "[class*='load-more'], [class*='show-more'], button[onclick*='load']")
                if load_more_button.is_displayed() and load_more_button.is_enabled():
                    driver.execute_script("arguments[0].click();", load_more_button)
                    self.update_signal.emit("'Daha fazla yükle' butonuna tıklandı")
                    time.sleep(3)
            except:
                pass  # Buton yoksa devam et

        if scroll_attempts >= max_scroll_attempts:
            self.update_signal.emit(f"Maksimum kaydırma sayısına ulaşıldı ({max_scroll_attempts})")

        # Son ürün sayısını bildir
        final_products = len(driver.find_elements(By.CSS_SELECTOR, "ul.grid > div"))
        self.update_signal.emit(f"Toplam {final_products} ürün konteyneri yüklendi")

class ProductListItem(QWidget):
    def __init__(self, product):
        super().__init__()
        self.product = product
        self.initUI()
        
    def initUI(self):
        layout = QHBoxLayout()
        
        # Product image
        image_label = QLabel()
        if self.product.image_data:
            pixmap = QPixmap()
            pixmap.loadFromData(self.product.image_data)
            pixmap = pixmap.scaled(80, 80, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            image_label.setPixmap(pixmap)
        else:
            image_label.setText("No Image")
        image_label.setFixedSize(80, 80)
        
        # Product info
        info_layout = QVBoxLayout()
        
        name_label = QLabel(self.product.name)
        name_label.setWordWrap(True)
        
        price_label = QLabel(self.product.price)
        price_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
        
        info_layout.addWidget(name_label)
        info_layout.addWidget(price_label)
        
        layout.addWidget(image_label)
        layout.addLayout(info_layout, 1)
        
        self.setLayout(layout)

class A101Scraper(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("A101 10 TL Ürünleri")
        self.setGeometry(100, 100, 800, 600)
        self.products = []
        self.initUI()
        
    def initUI(self):
        main_widget = QWidget()
        layout = QVBoxLayout()
        
        # Title
        title_label = QLabel("A101 10 TL Ürünleri")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18pt; font-weight: bold;")
        
        # Scrape button
        self.scrape_btn = QPushButton("Ürünleri Getir")
        self.scrape_btn.clicked.connect(self.start_scraping)
        
        # Progress bar
        self.progress = QProgressBar()
        self.progress.setVisible(False)
        
        # Results list
        self.results_list = QListWidget()
        self.results_list.setSpacing(5)
        
        # Status label
        self.status_label = QLabel("Ürünleri getirmek için butona tıklayın")
        self.status_label.setAlignment(Qt.AlignCenter)
        
        # Log area
        self.log_list = QListWidget()
        self.log_list.setMaximumHeight(150)
        
        # Add widgets to layout
        layout.addWidget(title_label)
        layout.addWidget(self.scrape_btn)
        layout.addWidget(self.progress)
        layout.addWidget(self.results_list, 1)
        layout.addWidget(QLabel("Log:"))
        layout.addWidget(self.log_list)
        layout.addWidget(self.status_label)
        
        main_widget.setLayout(layout)
        self.setCentralWidget(main_widget)
    
    def start_scraping(self):
        self.results_list.clear()
        self.log_list.clear()
        self.products = []
        self.scrape_btn.setEnabled(False)
        self.progress.setVisible(True)
        self.progress.setRange(0, 0)  # Indeterminate progress
        self.status_label.setText("Ürünler getiriliyor...")
        
        self.thread = ScraperThread()
        self.thread.update_signal.connect(self.update_log)
        self.thread.product_signal.connect(self.add_product)
        self.thread.finished_signal.connect(self.scraping_finished)
        self.thread.start()
    
    def update_log(self, message):
        self.log_list.addItem(message)
        self.log_list.scrollToBottom()
    
    def add_product(self, product):
        self.products.append(product)
        
        # Create custom widget item
        item = QListWidgetItem()
        product_widget = ProductListItem(product)
        item.setSizeHint(product_widget.sizeHint())
        
        self.results_list.addItem(item)
        self.results_list.setItemWidget(item, product_widget)
    
    def scraping_finished(self, products):
        self.scrape_btn.setEnabled(True)
        self.progress.setVisible(False)
        self.status_label.setText(f"Toplam {len(products)} ürün bulundu")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = A101Scraper()
    window.show()
    sys.exit(app.exec_())










