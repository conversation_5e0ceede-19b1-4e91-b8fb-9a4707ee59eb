{"version": 3, "file": "BluetoothProcessor.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/modules/bluetooth/BluetoothProcessor.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAEH,+DAIuC;AAKvC,MAAa,kBAAkB;IAC7B,aAAa,CAAe;IAC5B,uBAAuB,CAAyB;IAEhD,YACE,YAA0B,EAC1B,sBAA8C;QAE9C,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;IACxD,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,MAA2C;QAE3C,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YAC/B,wCAAwC;YACxC,8FAA8F;YAC9F,oFAAoF;YACpF,MAAM,IAAI,sCAAwB,CAChC,gEAAgE,CACjE,CAAC;QACJ,CAAC;QACD,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACxE,iFAAiF;QACjF,uDAAuD;QACvD,oFAAoF;QACpF,MAAM,OAAO,CAAC,SAAS,CAAC,gBAAgB,CAAC,WAAW,CAClD,4BAA4B,CAC7B,CAAC;QACF,MAAM,OAAO,CAAC,SAAS,CAAC,gBAAgB,CAAC,WAAW,CAClD,2BAA2B,EAC3B;YACE,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,IAAI;SACxC,CACF,CAAC;QACF,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,iBAAiB,CACrB,MAA6C;QAE7C,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACxE,MAAM,OAAO,CAAC,SAAS,CAAC,gBAAgB,CAAC,WAAW,CAClD,4BAA4B,CAC7B,CAAC;QACF,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,8BAA8B,CAClC,MAA0D;QAE1D,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACxE,MAAM,OAAO,CAAC,SAAS,CAAC,gBAAgB,CAAC,WAAW,CAClD,mDAAmD,EACnD;YACE,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,iBAAiB,EAAE,MAAM,CAAC,iBAAiB;YAC3C,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;SAC1C,CACF,CAAC;QACF,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,qBAAqB,CACzB,MAAiD;QAEjD,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACxE,MAAM,OAAO,CAAC,SAAS,CAAC,gBAAgB,CAAC,WAAW,CAClD,0CAA0C,EAC1C;YACE,KAAK,EAAE,MAAM,CAAC,SAAS;SACxB,CACF,CAAC;QACF,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,8BAA8B,CAClC,MAA0D;QAE1D,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACxE,MAAM,OAAO,CAAC,SAAS,CAAC,gBAAgB,CAAC,WAAW,CAClD,kDAAkD,EAClD;YACE,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,IAAI,EAAE,YAAY;YAClB,IAAI,EAAE,MAAM,CAAC,IAAI;SAClB,CACF,CAAC;QACF,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,kBAAkB,CAAC,SAAoB;QACrC,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,oCAAoC,EAAE,CAAC,KAAK,EAAE,EAAE;YACrE,IAAI,CAAC,aAAa,CAAC,aAAa,CAC9B;gBACE,IAAI,EAAE,OAAO;gBACb,MAAM,EAAE,sCAAsC;gBAC9C,MAAM,EAAE;oBACN,OAAO,EAAE,SAAS,CAAC,EAAE;oBACrB,MAAM,EAAE,KAAK,CAAC,EAAE;oBAChB,OAAO,EAAE,KAAK,CAAC,OAAO;iBACvB;aACF,EACD,SAAS,CAAC,EAAE,CACb,CAAC;QACJ,CAAC,CAAC,CAAC;QACH,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAC3B,0CAA0C,EAC1C,CAAC,KAAK,EAAE,EAAE;YACR,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBAChC,OAAO;YACT,CAAC;YACD,IAAI,CAAC,aAAa,CAAC,aAAa,CAC9B;gBACE,IAAI,EAAE,OAAO;gBACb,MAAM,EAAE,mCAAmC;gBAC3C,MAAM,EAAE;oBACN,OAAO,EAAE,SAAS,CAAC,EAAE;oBACrB,OAAO,EAAE,KAAK,CAAC,OAAO;iBACvB;aACF,EACD,SAAS,CAAC,EAAE,CACb,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,yBAAyB,CAC7B,MAAqD;QAErD,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAExE,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,MAAM,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,WAAW,CAC3C,2BAA2B,EAC3B;gBACE,EAAE,EAAE,MAAM,CAAC,MAAM;gBACjB,QAAQ,EAAE,MAAM,CAAC,MAAM;aACxB,CACF,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,MAAM,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,WAAW,CAC3C,2BAA2B,EAC3B;gBACE,EAAE,EAAE,MAAM,CAAC,MAAM;aAClB,CACF,CAAC;QACJ,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;CACF;AAzJD,gDAyJC"}